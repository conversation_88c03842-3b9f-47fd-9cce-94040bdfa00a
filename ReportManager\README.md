# 报告管理系统

## 系统概述

这是一个基于DevExpress WinForm的报告管理系统，用于管理和查询各站点的流量计算机报告文件。

## 主要功能

1. **站点管理**: 从数据库动态加载站点列表
2. **流量计算机管理**: 根据选择的站点显示对应的流量计算机列表
3. **报告类型选择**: 支持多种报告类型（小时报告、日报告、周报告等）
4. **日期范围查询**: 支持按创建时间筛选文件
5. **文件列表显示**: 在表格中显示符合条件的PDF和Excel文件
6. **文件快速打开**: 双击文件可直接打开

## 系统配置

### 1. 数据库配置

在 `App.config` 文件中修改数据库连接字符串：

```xml
<connectionStrings>
  <add name="ReportDB" connectionString="Server=你的服务器地址;Database=数据库名;User ID=用户名;Password=******;..." />
</connectionStrings>
```

### 2. 报告路径配置

在 `App.config` 文件中修改报告根目录：

```xml
<add key="ReportRootPath" value="D:\Report\" />
```

### 3. 报告类型配置

在 `App.config` 文件中可以自定义报告类型：

```xml
<add key="ReportTypes" value="小时报告:HourlyReport;日报告:DailyReport;周报告:WeeklyReport" />
```

格式说明：`显示名称:文件夹名称`，多个类型用分号分隔。

## 数据库要求

系统需要连接到包含 `Selection_Table` 表的SQL Server数据库，表结构应包含：
- `Station`: 站点名称
- `FlowComputerTag`: 流量计算机标签

## 报告文件路径规则

系统按以下规则构建报告文件路径：
```
报告根目录 + 站名 + 流量计算机名(下划线转短横线) + 报告类型文件夹
```

例如：
```
D:\Report\东部电厂\FC-11670A\HourlyReport\
```

## 支持的文件格式

- PDF文件 (*.pdf)
- Excel文件 (*.xls, *.xlsx)

## 使用说明

1. **启动应用**: 运行ReportManager.exe
2. **选择站点**: 从第一个下拉框选择站点
3. **选择流量计算机**: 从第二个下拉框选择流量计算机
4. **选择报告类型**: 从第三个下拉框选择报告类型
5. **设置日期范围**: 选择开始日期和结束日期
6. **查询文件**: 点击"查询报告文件"按钮
7. **查看结果**: 在表格中查看找到的文件列表
8. **打开文件**: 双击表格中的文件即可打开

## 注意事项

1. 首次使用前，请确保数据库连接配置正确
2. 确保报告根目录路径存在且有访问权限
3. 如果查询大量文件，可能需要等待一段时间
4. 系统会自动验证输入的有效性并显示相应提示

## 技术特性

- **数据库安全**: 使用参数化查询防止SQL注入
- **异常处理**: 完善的错误处理和用户提示
- **资源管理**: 正确的数据库连接和文件资源管理
- **用户友好**: 直观的界面和操作流程

## 系统要求

- .NET Framework 4.8
- DevExpress Controls v24.2
- SQL Server数据库连接
- Windows操作系统 