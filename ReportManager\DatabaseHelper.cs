using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Threading.Tasks;

namespace ReportManager
{
    public class DatabaseHelper
    {
        private string _currentConnectionString;
        private readonly string _primaryConnectionString;
        private readonly string _secondaryConnectionString;
        private readonly string _tableName;
        private readonly int _testTimeout;
        private string _currentConnectionName;
        private bool _isInitialized;
        private Task _initializationTask;

        public DatabaseHelper()
        {
            // 读取两个数据库连接字符串
            _primaryConnectionString = ConfigurationManager.ConnectionStrings["ReportDB_Primary"]?.ConnectionString;
            _secondaryConnectionString = ConfigurationManager.ConnectionStrings["ReportDB_Secondary"]?.ConnectionString;
            _tableName = ConfigurationManager.AppSettings["SelectionTableName"];
            
            // 读取数据库连接检测超时时间，默认为5秒
            if (!int.TryParse(ConfigurationManager.AppSettings["DatabaseTestTimeout"], out _testTimeout))
            {
                _testTimeout = 5;
            }

            // 验证配置
            if (string.IsNullOrEmpty(_primaryConnectionString))
            {
                throw new ConfigurationErrorsException("未找到主数据库连接字符串 'ReportDB_Primary'，请检查配置文件。");
            }
            
            if (string.IsNullOrEmpty(_secondaryConnectionString))
            {
                throw new ConfigurationErrorsException("未找到备用数据库连接字符串 'ReportDB_Secondary'，请检查配置文件。");
            }

            // 标记为未初始化
            _isInitialized = false;
        }

        /// <summary>
        /// 异步初始化数据库连接
        /// </summary>
        /// <returns></returns>
        public Task InitializeAsync()
        {
            if (_initializationTask == null)
            {
                _initializationTask = Task.Run(() =>
                {
                    try
                    {
                        DetectAvailableConnection();
                        _isInitialized = true;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"数据库初始化异常: {ex.Message}");
                        // 不重新抛出异常，让程序继续运行
                    }
                });
            }
            return _initializationTask;
        }

        /// <summary>
        /// 检查数据库是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 等待数据库初始化完成
        /// </summary>
        /// <returns></returns>
        public async Task WaitForInitializationAsync()
        {
            if (_initializationTask != null)
            {
                await _initializationTask;
            }
        }

        /// <summary>
        /// 检测并设置可用的数据库连接
        /// </summary>
        private void DetectAvailableConnection()
        {
            // 首先尝试主数据库
            if (TestConnectionString(_primaryConnectionString))
            {
                _currentConnectionString = _primaryConnectionString;
                _currentConnectionName = "主数据库";
                return;
            }

            // 主数据库不可用，尝试备用数据库
            if (TestConnectionString(_secondaryConnectionString))
            {
                _currentConnectionString = _secondaryConnectionString;
                _currentConnectionName = "备用数据库";
                return;
            }

            // 两个数据库都不可用
            throw new InvalidOperationException($"无法连接到任何数据库服务器。\n主数据库: {GetServerFromConnectionString(_primaryConnectionString)}\n备用数据库: {GetServerFromConnectionString(_secondaryConnectionString)}\n请检查网络连接和数据库服务器状态。");
        }

        /// <summary>
        /// 测试指定连接字符串的数据库连接
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>连接是否成功</returns>
        private bool TestConnectionString(string connectionString)
        {
            try
            {
                // 创建一个临时的连接字符串，使用配置的超时时间
                var builder = new SqlConnectionStringBuilder(connectionString)
                {
                    ConnectTimeout = _testTimeout // 设置连接超时为配置的时间
                };
                
                using (var connection = new SqlConnection(builder.ConnectionString))
                {
                    connection.Open();
                    // 设置命令超时（虽然SELECT 1很快，但保持一致性）
                    using (var command = new SqlCommand("SELECT 1", connection))
                    {
                        command.CommandTimeout = _testTimeout;
                        command.ExecuteScalar();
                    }
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从连接字符串中提取服务器地址
        /// </summary>
        /// <param name="connectionString">连接字符串</param>
        /// <returns>服务器地址</returns>
        private string GetServerFromConnectionString(string connectionString)
        {
            try
            {
                var builder = new SqlConnectionStringBuilder(connectionString);
                return builder.DataSource ?? "未知服务器";
            }
            catch
            {
                return "解析失败";
            }
        }

        /// <summary>
        /// 获取当前使用的数据库连接信息
        /// </summary>
        /// <returns>连接信息描述</returns>
        public string GetCurrentConnectionInfo()
        {
            if (string.IsNullOrEmpty(_currentConnectionString))
            {
                return "未连接到数据库";
            }

            var server = GetServerFromConnectionString(_currentConnectionString);
            return $"{_currentConnectionName} ({server})";
        }

        /// <summary>
        /// 执行数据库操作前检查连接状态，如果当前连接失效则自动切换
        /// </summary>
        private void EnsureConnection()
        {
            // 如果数据库未初始化，先等待初始化完成
            if (!_isInitialized)
            {
                _initializationTask?.Wait();
            }

            // 如果当前连接不可用，重新检测
            if (string.IsNullOrEmpty(_currentConnectionString) || !TestConnectionString(_currentConnectionString))
            {
                DetectAvailableConnection();
            }
        }

        /// <summary>
        /// 异步版本的连接检查
        /// </summary>
        private async Task EnsureConnectionAsync()
        {
            // 如果数据库未初始化，先等待初始化完成
            if (!_isInitialized)
            {
                await WaitForInitializationAsync();
            }

            // 如果当前连接不可用，重新检测
            if (string.IsNullOrEmpty(_currentConnectionString) || !TestConnectionString(_currentConnectionString))
            {
                DetectAvailableConnection();
            }
        }

        /// <summary>
        /// 获取所有不重复的站点名称
        /// </summary>
        /// <returns></returns>
        public List<string> GetDistinctStations()
        {
            var stations = new List<string>();
            
            try
            {
                // 确保连接可用
                EnsureConnection();
                
                using (var connection = new SqlConnection(_currentConnectionString))
                {
                    connection.Open();
                    string query = $"SELECT DISTINCT Station FROM {_tableName} WHERE Station IS NOT NULL ORDER BY Station";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                stations.Add(reader["Station"].ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取站点数据时发生错误 (当前连接: {GetCurrentConnectionInfo()}): {ex.Message}", ex);
            }
            
            return stations;
        }

        /// <summary>
        /// 异步获取所有不重复的站点名称
        /// </summary>
        /// <returns></returns>
        public async Task<List<string>> GetDistinctStationsAsync()
        {
            var stations = new List<string>();
            
            try
            {
                // 确保连接可用
                await EnsureConnectionAsync();
                
                using (var connection = new SqlConnection(_currentConnectionString))
                {
                    await connection.OpenAsync();
                    string query = $"SELECT DISTINCT Station FROM {_tableName} WHERE Station IS NOT NULL ORDER BY Station";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                stations.Add(reader["Station"].ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取站点数据时发生错误 (当前连接: {GetCurrentConnectionInfo()}): {ex.Message}", ex);
            }
            
            return stations;
        }

        /// <summary>
        /// 根据站点名称获取对应的流量计算机标签
        /// </summary>
        /// <param name="stationName"></param>
        /// <returns></returns>
        public List<string> GetFlowComputerTagsByStation(string stationName)
        {
            var flowComputerTags = new List<string>();
            
            try
            {
                // 确保连接可用
                EnsureConnection();
                
                using (var connection = new SqlConnection(_currentConnectionString))
                {
                    connection.Open();
                    string query = $"SELECT DISTINCT FlowComputerTag FROM {_tableName} WHERE Station = @Station AND FlowComputerTag IS NOT NULL ORDER BY FlowComputerTag";
                    
                    using (var command = new SqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@Station", stationName);
                        
                        using (var reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                flowComputerTags.Add(reader["FlowComputerTag"].ToString());
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取流量计算机标签数据时发生错误 (当前连接: {GetCurrentConnectionInfo()}): {ex.Message}", ex);
            }
            
            return flowComputerTags;
        }

        /// <summary>
        /// 测试数据库连接
        /// </summary>
        /// <returns></returns>
        public bool TestConnection()
        {
            try
            {
                // 重新检测可用连接
                DetectAvailableConnection();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 强制重新检测数据库连接
        /// </summary>
        /// <returns>检测结果信息</returns>
        public string RefreshConnection()
        {
            try
            {
                DetectAvailableConnection();
                return $"连接检测成功，当前使用: {GetCurrentConnectionInfo()}";
            }
            catch (Exception ex)
            {
                return $"连接检测失败: {ex.Message}";
            }
        }
    }
} 