﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraEditors;
using System.Configuration;

namespace ReportManager
{
    public partial class Form1 : DevExpress.XtraEditors.XtraForm
    {
        private readonly DatabaseHelper _databaseHelper;
        private readonly FileHelper _fileHelper;
        private bool isExiting = false;
        private bool _isDatabaseInitializing = true;

        public Form1()
        {
            InitializeComponent();
            _databaseHelper = new DatabaseHelper();
            _fileHelper = new FileHelper();

            // 立即初始化窗体（不等待数据库连接）
            InitializeFormImmediate();
            
            // 异步初始化数据库连接
            _ = InitializeDatabaseAsync();
        }

        // 注释掉自定义消息处理（不再需要托盘激活）
        /*
        /// <summary>
        /// 重写WndProc方法以处理自定义Windows消息
        /// </summary>
        /// <param name="m"></param>
        protected override void WndProc(ref Message m)
        {
            // 自定义消息常量（与Program.cs中保持一致）
            const int WM_USER = 0x0400;
            const int WM_SHOWWINDOW_CUSTOM = WM_USER + 1;

            // 检查是否接收到激活窗口的自定义消息
            if (m.Msg == WM_SHOWWINDOW_CUSTOM)
            {
                // 显示窗口
                ShowForm();
                return;
            }

            // 调用基类的WndProc处理其他消息
            base.WndProc(ref m);
        }
        */

        /// <summary>
        /// 立即初始化窗体（不依赖数据库的部分）
        /// </summary>
        private void InitializeFormImmediate()
        {
            try
            {
                // 设置ComboBox控件为不可编辑状态（只能下拉选择）
                cbStation.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
                cbFlowComputer.Properties.TextEditStyle =
                    DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
                cbReportType.Properties.TextEditStyle =
                    DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;

                // 设置ComboBox的默认提示文本
                cbStation.Properties.NullText = "数据库连接中...";
                cbFlowComputer.Properties.NullText = "请先选择计量站";

                // 设置日期控件默认值：结束日期为今天，开始日期为一周前
                dateEnd.EditValue = DateTime.Today;
                dateStart.EditValue = DateTime.Today.AddDays(-7);

                // 设置日期控件行为：点击输入框直接弹出日期选择器
                dateStart.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
                dateEnd.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;

                // 设置日期控件显示格式
                dateStart.Properties.DisplayFormat.FormatString = "yyyy-MM-dd";
                dateStart.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                dateStart.Properties.EditFormat.FormatString = "yyyy-MM-dd";
                dateStart.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;

                dateEnd.Properties.DisplayFormat.FormatString = "yyyy-MM-dd";
                dateEnd.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                dateEnd.Properties.EditFormat.FormatString = "yyyy-MM-dd";
                dateEnd.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.DateTime;

                // 初始化GridView列
                InitializeGridView();

                // 加载不依赖数据库的数据
                LoadReportTypes();

                // 绑定事件
                cbStation.SelectedIndexChanged += CbStation_SelectedIndexChanged;
                btnShowReport.Click += BtnShowReport_Click;

                // 绑定键盘事件以支持真正退出程序（Ctrl+Shift+Q）
                this.KeyPreview = true;
                this.KeyDown += Form1_KeyDown;

                // 设置窗体标题提示（显示数据库连接中状态）
                this.Text = "报告管理系统 - 数据库连接中... - 按 Ctrl+Shift+Q 退出";

                // 检查报告路径配置
                if (!_fileHelper.RefreshAvailableReportPath())
                {
                    var pathStatusInfo = _fileHelper.GetPathStatusInfo();
                    XtraMessageBox.Show(
                        $"警告：报告路径配置不可用！\n\n{pathStatusInfo}\n\n请检查配置文件中的路径设置。",
                        "路径配置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"初始化窗体时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 异步初始化数据库连接和相关数据
        /// </summary>
        private async Task InitializeDatabaseAsync()
        {
            try
            {
                // 开始数据库初始化
                await _databaseHelper.InitializeAsync();

                // 在UI线程上更新界面
                this.Invoke(new Action(async () =>
                {
                    try
                    {
                        if (_databaseHelper.IsInitialized)
                        {
                            // 显示当前使用的数据库连接信息
                            var connectionInfo = _databaseHelper.GetCurrentConnectionInfo();
                            System.Diagnostics.Debug.WriteLine($"数据库连接成功: {connectionInfo}");

                            // 更新窗体标题
                            this.Text = $"报告管理系统 - {connectionInfo} - 按 Ctrl+Shift+Q 退出";

                            // 更新站点选择提示
                            cbStation.Properties.NullText = "请选择计量站";

                            // 加载站点数据
                            await LoadStationsAsync();
                        }
                        else
                        {
                            // 数据库连接失败
                            this.Text = "报告管理系统 - 数据库连接失败 - 按 Ctrl+Shift+Q 退出";
                            cbStation.Properties.NullText = "数据库连接失败";
                            
                            XtraMessageBox.Show("数据库连接失败，请检查配置！\n程序将以离线模式运行。", "连接失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"UI更新异常: {ex.Message}");
                        this.Text = "报告管理系统 - 数据库连接异常 - 按 Ctrl+Shift+Q 退出";
                        cbStation.Properties.NullText = "数据库连接异常";
                    }
                    finally
                    {
                        _isDatabaseInitializing = false;
                    }
                }));
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"数据库初始化异常: {ex.Message}");
                
                // 在UI线程上显示错误
                this.Invoke(new Action(() =>
                {
                    this.Text = "报告管理系统 - 数据库初始化失败 - 按 Ctrl+Shift+Q 退出";
                    cbStation.Properties.NullText = "数据库初始化失败";
                    _isDatabaseInitializing = false;
                }));
            }
        }

        /// <summary>
        /// 初始化GridView列
        /// </summary>
        private void InitializeGridView()
        {
            gridView1.Columns.Clear();

            // 文件名列
            var colFileName = new GridColumn
            {
                FieldName = "FileName",
                Caption = "文件名",
                Visible = true,
                Width = 350
            };
            gridView1.Columns.Add(colFileName);

            // 文件名日期列（解析出的日期）
            var colFileNameDate = new GridColumn
            {
                FieldName = "FormattedFileNameDate",
                Caption = "报告日期",
                Visible = true,
                Width = 150
            };
            gridView1.Columns.Add(colFileNameDate);

            // 创建时间列
            var colCreationTime = new GridColumn
            {
                FieldName = "FormattedCreationTime",
                Caption = "创建时间",
                Visible = true,
                Width = 150
            };
            gridView1.Columns.Add(colCreationTime);

            // 文件大小列
            var colFileSize = new GridColumn
            {
                FieldName = "FormattedFileSize",
                Caption = "文件大小",
                Visible = true,
                Width = 100
            };
            gridView1.Columns.Add(colFileSize);

            // 完整路径列(隐藏)
            var colFullPath = new GridColumn
            {
                FieldName = "FullPath",
                Caption = "完整路径",
                Visible = false
            };
            gridView1.Columns.Add(colFullPath);

            // 设置GridView属性
            gridView1.OptionsView.ShowGroupPanel = false;
            gridView1.OptionsSelection.MultiSelect = false;
            gridView1.OptionsSelection.MultiSelectMode = DevExpress.XtraGrid.Views.Grid.GridMultiSelectMode.RowSelect;

            // 设置GridView为只读状态
            gridView1.OptionsBehavior.Editable = false;
            gridView1.OptionsBehavior.ReadOnly = true;

            // 设置字体样式
            // 列标题字体：加大并加粗
            gridView1.Appearance.HeaderPanel.Font = new Font("Microsoft YaHei", 12F, FontStyle.Bold);
            gridView1.Appearance.HeaderPanel.Options.UseFont = true;

            // 表格内容字体：加大
            gridView1.Appearance.Row.Font = new Font("Microsoft YaHei", 11F, FontStyle.Regular);
            gridView1.Appearance.Row.Options.UseFont = true;

            // 选中行字体
            gridView1.Appearance.SelectedRow.Font = new Font("Microsoft YaHei", 11F, FontStyle.Regular);
            gridView1.Appearance.SelectedRow.Options.UseFont = true;

            // 焦点行字体
            gridView1.Appearance.FocusedRow.Font = new Font("Microsoft YaHei", 11F, FontStyle.Regular);
            gridView1.Appearance.FocusedRow.Options.UseFont = true;

            // 设置行高自动调整
            gridView1.OptionsView.RowAutoHeight = true;

            // 双击打开文件
            gridView1.DoubleClick += GridView1_DoubleClick;
        }

        /// <summary>
        /// 加载站点列表
        /// </summary>
        private void LoadStations()
        {
            try
            {
                var stations = _databaseHelper.GetDistinctStations();
                cbStation.Properties.Items.Clear();

                // 首先添加固定的"色谱分析仪"选项
                cbStation.Properties.Items.Add("色谱分析仪");

                // 然后添加数据库中的站点
                cbStation.Properties.Items.AddRange(stations.ToArray());

                // 不自动选择第一项，显示默认提示文本
                cbStation.EditValue = null;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载站点列表时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 异步加载站点列表
        /// </summary>
        private async Task LoadStationsAsync()
        {
            try
            {
                var stations = await _databaseHelper.GetDistinctStationsAsync();
                cbStation.Properties.Items.Clear();

                // 首先添加固定的"色谱分析仪"选项
                cbStation.Properties.Items.Add("色谱分析仪");

                // 然后添加数据库中的站点
                cbStation.Properties.Items.AddRange(stations.ToArray());

                // 不自动选择第一项，显示默认提示文本
                cbStation.EditValue = null;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载站点列表时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                cbStation.Properties.NullText = "站点加载失败";
            }
        }

        /// <summary>
        /// 加载报告类型列表
        /// </summary>
        private void LoadReportTypes()
        {
            try
            {
                var reportTypes = _fileHelper.GetReportTypes();
                cbReportType.Properties.Items.Clear();
                cbReportType.Properties.Items.AddRange(reportTypes.ToArray());

                if (reportTypes.Count > 0)
                {
                    cbReportType.SelectedIndex = 0;
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载报告类型列表时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 站点选择变化事件
        /// </summary>
        private void CbStation_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                var selectedStation = cbStation.Text;
                if (!string.IsNullOrEmpty(selectedStation))
                {
                    if (selectedStation == "色谱分析仪")
                    {
                        // 色谱分析仪模式
                        HandleGCStationSelection();
                    }
                    else
                    {
                        // 常规站点模式
                        HandleNormalStationSelection(selectedStation);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载流量计算机列表时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 处理色谱分析仪站点选择
        /// </summary>
        private void HandleGCStationSelection()
        {
            try
            {
                // 禁用报告类型选择
                cbReportType.Enabled = false;
                cbReportType.EditValue = null;
                cbReportType.Properties.NullText = "色谱分析仪无需选择报告类型";

                // 加载GC设备代码到流量计算机下拉框
                var gcDeviceCodes = _fileHelper.GetGCDeviceCodes();
                cbFlowComputer.Properties.Items.Clear();

                if (gcDeviceCodes != null && gcDeviceCodes.Count > 0)
                {
                    cbFlowComputer.Properties.Items.AddRange(gcDeviceCodes.ToArray());
                    cbFlowComputer.Properties.NullText = "请选择GC设备代码";
                }
                else
                {
                    cbFlowComputer.Properties.NullText = "未配置GC设备代码";
                    XtraMessageBox.Show("未找到GC设备代码配置，请检查App.config中的GCDeviceCodes配置项。",
                        "配置警告", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }

                // 不自动选择第一项
                cbFlowComputer.EditValue = null;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载GC设备代码时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                cbFlowComputer.Properties.NullText = "GC设备代码加载失败";
            }
        }

        /// <summary>
        /// 处理常规站点选择
        /// </summary>
        /// <param name="selectedStation"></param>
        private void HandleNormalStationSelection(string selectedStation)
        {
            try
            {
                // 启用报告类型选择
                cbReportType.Enabled = true;
                cbReportType.Properties.NullText = "请选择报告类型";

                // 从数据库加载流量计算机标签
                var flowComputerTags = _databaseHelper.GetFlowComputerTagsByStation(selectedStation);
                cbFlowComputer.Properties.Items.Clear();

                if (flowComputerTags != null && flowComputerTags.Count > 0)
                {
                    cbFlowComputer.Properties.Items.AddRange(flowComputerTags.ToArray());
                    cbFlowComputer.Properties.NullText = "请选择流量计算机";
                }
                else
                {
                    cbFlowComputer.Properties.NullText = $"站点 '{selectedStation}' 无流量计算机数据";
                }

                // 不自动选择第一项
                cbFlowComputer.EditValue = null;
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"加载站点 '{selectedStation}' 的流量计算机列表时发生错误: {ex.Message}",
                    "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                cbFlowComputer.Properties.NullText = "流量计算机加载失败";
            }
        }

        /// <summary>
        /// 查询按钮点击事件
        /// </summary>
        private async void BtnShowReport_Click(object sender, EventArgs e)
        {
            try
            {
                // 检查数据库是否正在初始化
                if (_isDatabaseInitializing)
                {
                    XtraMessageBox.Show("数据库正在连接中，请稍等片刻后再试。", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // 检查数据库是否已成功初始化
                if (!_databaseHelper.IsInitialized)
                {
                    XtraMessageBox.Show("数据库连接失败，无法进行查询操作。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 严格验证输入
                // 检查站点是否已选择（不是默认提示状态）
                if (cbStation.EditValue == null || cbStation.SelectedIndex < 0)
                {
                    XtraMessageBox.Show("请选择计量站！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cbStation.Focus();
                    return;
                }

                // 获取选择的站点名称
                var selectedStation = cbStation.Text;

                // 检查流量计算机是否已选择（不是默认提示状态）
                if (cbFlowComputer.EditValue == null || cbFlowComputer.SelectedIndex < 0)
                {
                    var promptMessage = selectedStation == "色谱分析仪" ? "请选择GC设备代码！" : "请选择流量计算机！";
                    XtraMessageBox.Show(promptMessage, "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    cbFlowComputer.Focus();
                    return;
                }

                // 对于色谱分析仪，额外验证设备代码的有效性
                if (selectedStation == "色谱分析仪")
                {
                    var selectedDeviceCode = cbFlowComputer.Text;
                    var availableDeviceCodes = _fileHelper.GetGCDeviceCodes();
                    if (availableDeviceCodes == null || !availableDeviceCodes.Contains(selectedDeviceCode))
                    {
                        XtraMessageBox.Show($"选择的GC设备代码 '{selectedDeviceCode}' 无效，请重新选择！",
                            "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        cbFlowComputer.Focus();
                        return;
                    }
                }

                // 检查报告类型是否已选择（色谱分析仪模式下跳过此检查）
                if (selectedStation != "色谱分析仪")
                {
                    if (string.IsNullOrEmpty(cbReportType.Text) || cbReportType.SelectedIndex < 0)
                    {
                        XtraMessageBox.Show("请选择报告类型！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        cbReportType.Focus();
                        return;
                    }
                }

                // 检查日期是否已选择
                if (dateStart.EditValue == null || dateEnd.EditValue == null)
                {
                    XtraMessageBox.Show("请选择日期范围！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    if (dateStart.EditValue == null)
                        dateStart.Focus();
                    else
                        dateEnd.Focus();
                    return;
                }

                var startDate = (DateTime)dateStart.EditValue;
                var endDate = (DateTime)dateEnd.EditValue;

                // 检查日期范围是否正确
                if (startDate.Date > endDate.Date)
                {
                    XtraMessageBox.Show("开始日期不能大于结束日期！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    dateStart.Focus();
                    return;
                }

                // 检查结束日期是否为未来日期
                if (endDate.Date > DateTime.Today)
                {
                    XtraMessageBox.Show("结束日期不能是未来日期！", "提示", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    dateEnd.Focus();
                    return;
                }

                // 检查日期范围是否合理（可选：防止查询过长时间范围）
                var daysDifference = (endDate.Date - startDate.Date).Days;
                if (daysDifference > 365)
                {
                    var result = XtraMessageBox.Show($"您选择的日期范围跨度为 {daysDifference} 天，查询可能需要较长时间。\n\n是否继续查询？",
                        "确认", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.No)
                    {
                        return;
                    }
                }

                // 构建报告路径
                string reportPath;
                try
                {
                    if (selectedStation == "色谱分析仪")
                    {
                        // 色谱分析仪使用特殊的路径构建方法
                        reportPath = _fileHelper.BuildGCReportPath(cbFlowComputer.Text);
                    }
                    else
                    {
                        // 常规报告路径构建
                        reportPath = _fileHelper.BuildReportPath(cbStation.Text, cbFlowComputer.Text, cbReportType.Text);
                    }
                }
                catch (InvalidOperationException ex)
                {
                    // 如果报告路径不可用，显示详细的路径状态信息
                    var pathStatusInfo = _fileHelper.GetPathStatusInfo();
                    XtraMessageBox.Show(
                        $"报告路径配置错误或无法访问！\n\n{pathStatusInfo}\n\n错误信息：{ex.Message}",
                        "路径配置错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // 显示查询路径（保留退出提示）
                this.Text = $"报告管理系统 - 查询路径：{reportPath} (按 Ctrl+Shift+Q 退出)";

                // 禁用查询按钮并显示进度
                btnShowReport.Enabled = false;
                btnShowReport.Text = "查询中...";

                try
                {
                    // 获取详细的调试信息（用于调试和问题排查）
                    var debugInfo = _fileHelper.GetQueryDebugInfo(reportPath, startDate, endDate);
                    System.Diagnostics.Debug.WriteLine($"查询调试信息:\n{debugInfo}");

                    // 异步查询文件（包含归档查询）
                    var reportFiles = await _fileHelper.GetReportFilesAsync(reportPath, startDate, endDate);

                    // 绑定数据到GridView
                    gridControl1.DataSource = reportFiles;

                    // 显示查询结果统计
                    var resultMessage = $"查询完成，共找到 {reportFiles.Count} 个文件";
                    if (reportFiles.Count > 0)
                    {
                        var archiveThreshold = _fileHelper.GetArchiveThresholdDate();
                        var archivedFiles = reportFiles.Count(f => f.CreationTime < archiveThreshold);
                        var currentFiles = reportFiles.Count - archivedFiles;

                        resultMessage += $"\n当前文件: {currentFiles} 个";
                        if (archivedFiles > 0)
                        {
                            resultMessage += $"\n归档文件: {archivedFiles} 个";
                        }
                    }

                    this.Text = $"报告管理系统 - {resultMessage} (按 Ctrl+Shift+Q 退出)";

                    // 检查查询结果，如果为0则提示用户
                    if (reportFiles.Count == 0)
                    {
                        XtraMessageBox.Show(
                            $"在指定的日期范围内未找到报告文件。\n\n查询路径：{reportPath}\n日期范围：{startDate:yyyy-MM-dd} 至 {endDate:yyyy-MM-dd}\n\n已同时搜索当前目录和归档子目录。\n\n=== 详细调试信息 ===\n{debugInfo}",
                            "未找到报告", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                finally
                {
                    // 恢复查询按钮
                    btnShowReport.Enabled = true;
                    btnShowReport.Text = "查询";
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"查询文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                gridControl1.DataSource = null;
                this.Text = "报告管理系统 - 关闭窗口最小化到任务栏，按 Ctrl+Shift+Q 真正退出";
            }
        }

        /// <summary>
        /// 获取当前运行的Excel进程数量
        /// </summary>
        /// <returns>Excel进程数量</returns>
        private int GetExcelProcessCount()
        {
            try
            {
                var excelProcesses = System.Diagnostics.Process.GetProcessesByName("EXCEL");
                return excelProcesses.Length;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Excel进程数量时发生错误: {ex.Message}");
                return 0; // 出错时返回0，不阻止文件打开
            }
        }

        /// <summary>
        /// 检查是否为Excel文件
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否为Excel文件</returns>
        private bool IsExcelFile(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
                return false;

            var extension = System.IO.Path.GetExtension(filePath).ToLower();
            return extension == ".xls" || extension == ".xlsx" || extension == ".xlsm";
        }

        /// <summary>
        /// 关闭所有Excel进程
        /// </summary>
        /// <returns>成功关闭的进程数量</returns>
        private int CloseAllExcelProcesses()
        {
            int closedCount = 0;
            try
            {
                var excelProcesses = System.Diagnostics.Process.GetProcessesByName("EXCEL");
                foreach (var process in excelProcesses)
                {
                    try
                    {
                        process.CloseMainWindow(); // 尝试优雅关闭
                        if (!process.WaitForExit(3000)) // 等待3秒
                        {
                            process.Kill(); // 强制关闭
                        }
                        closedCount++;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"关闭Excel进程时发生错误: {ex.Message}");
                    }
                    finally
                    {
                        process.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"获取Excel进程列表时发生错误: {ex.Message}");
            }
            return closedCount;
        }

        /// <summary>
        /// GridView双击事件 - 打开文件
        /// </summary>
        private void GridView1_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                var selectedRowHandle = gridView1.FocusedRowHandle;
                if (selectedRowHandle >= 0)
                {
                    var fullPath = gridView1.GetRowCellValue(selectedRowHandle, "FullPath")?.ToString();
                    if (!string.IsNullOrEmpty(fullPath))
                    {
                        // 检查是否为Excel文件，如果是则进行进程数量检测
                        if (IsExcelFile(fullPath))
                        {
                            var excelCount = GetExcelProcessCount();
                            if (excelCount >= 5)
                            {
                                // 显示选择对话框
                                var result = XtraMessageBox.Show(
                                    $"检测到当前已打开 {excelCount} 个Excel文件，继续打开可能会导致系统卡顿。\n\n" +
                                    "是否要先关闭已打开的Excel文件？\n\n" +
                                    "• 是：关闭所有已打开的Excel文件，然后打开当前文件\n" +
                                    "• 否：直接打开当前文件（可能导致卡顿）\n" +
                                    "• 取消：不打开当前文件",
                                    "Excel文件过多提醒",
                                    MessageBoxButtons.YesNoCancel,
                                    MessageBoxIcon.Warning);

                                if (result == DialogResult.Yes)
                                {
                                    // 用户选择关闭已打开的Excel文件
                                    var closedCount = CloseAllExcelProcesses();
                                    if (closedCount > 0)
                                    {
                                        XtraMessageBox.Show($"已关闭 {closedCount} 个Excel进程，现在打开选中的文件。",
                                            "操作完成", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                    }
                                    // 等待一下让系统释放资源
                                    System.Threading.Thread.Sleep(1000);
                                }
                                else if (result == DialogResult.Cancel)
                                {
                                    // 用户选择取消，不打开文件
                                    return;
                                }
                                // 如果选择"否"，则继续执行打开文件的操作
                            }
                        }

                        // 打开文件
                        System.Diagnostics.Process.Start(fullPath);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"打开文件时发生错误: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void Form1_FormClosing(object sender, FormClosingEventArgs e)
        {
            if (!isExiting)
            {
                // 取消关闭事件，改为最小化到任务栏
                e.Cancel = true;

                // 最小化到任务栏
                this.WindowState = FormWindowState.Minimized;
            }
        }



        private void Form1_KeyDown(object sender, KeyEventArgs e)
        {
            // 检查是否按下 Ctrl+Shift+Q 组合键来真正退出程序
            if (e.Control && e.Shift && e.KeyCode == Keys.Q)
            {
                var result = XtraMessageBox.Show("确定要退出程序吗？", "确认退出",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // 设置真正退出标志
                    isExiting = true;

                    // 关闭程序
                    this.Close();
                }

                e.Handled = true;
            }
        }

        // 注释掉托盘相关功能

        private void notifyIcon1_DoubleClick(object sender, EventArgs e)
        {
            ShowForm();
        }

        private void openToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ShowForm();
        }

        private void exitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            // 1. 將標誌設為 true，表示是真的要結束了
            isExiting = true;

            // 2. 呼叫 Close()，這次 FormClosing 事件將不會被取消，程式會正常關閉
            this.Close();
        }


        // 简化的ShowForm方法（仅用于从最小化状态恢复）
        private void ShowForm()
        {
            // 从最小化状态恢复到正常状态
            this.WindowState = FormWindowState.Normal;
            this.Activate();
            this.BringToFront();
        }


        // 注释掉Win32 API声明（不再需要）
        /*
        // 添加Win32 API声明
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool BringWindowToTop(IntPtr hWnd);
        */
    }
}