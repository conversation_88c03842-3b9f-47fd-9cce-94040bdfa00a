using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace ReportManager
{
    public class FileHelper
    {
        private readonly string _reportRootPath;
        private readonly string _reportRootPathBackup;
        private readonly Dictionary<string, string> _reportTypeMapping;
        private string _currentReportRootPath;
        private readonly int _archiveThresholdMonths;
        private readonly List<string> _gcDeviceCodes;

        public FileHelper()
        {
            _reportRootPath = ConfigurationManager.AppSettings["ReportRootPath"];
            _reportRootPathBackup = ConfigurationManager.AppSettings["ReportRootPathBackup"];
            _reportTypeMapping = LoadReportTypeMapping();
            _gcDeviceCodes = LoadGCDeviceCodes();

            // 初始化归档阈值月数，默认为3个月
            if (!int.TryParse(ConfigurationManager.AppSettings["ArchiveThresholdMonths"], out _archiveThresholdMonths))
            {
                _archiveThresholdMonths = 3;
            }

            // 初始化当前使用的路径
            _currentReportRootPath = GetAvailableReportPath();
        }

        /// <summary>
        /// 检查路径是否可访问
        /// </summary>
        /// <param name="path"></param>
        /// <returns></returns>
        private bool IsPathAccessible(string path)
        {
            try
            {
                if (string.IsNullOrEmpty(path))
                    return false;

                // 检查路径是否存在
                if (!Directory.Exists(path))
                    return false;

                // 尝试读取路径内容（测试读权限）
                var directory = new DirectoryInfo(path);
                directory.GetDirectories();
                directory.GetFiles();
                
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取可用的报告根路径
        /// </summary>
        /// <returns></returns>
        private string GetAvailableReportPath()
        {
            // 首先尝试主路径
            if (IsPathAccessible(_reportRootPath))
            {
                return _reportRootPath;
            }

            // 如果主路径不可用，尝试备用路径
            if (IsPathAccessible(_reportRootPathBackup))
            {
                return _reportRootPathBackup;
            }

            // 如果两个路径都不可用，返回null
            return null;
        }

        /// <summary>
        /// 检查并更新当前可用的报告根路径
        /// </summary>
        /// <returns>如果有可用路径返回true，否则返回false</returns>
        public bool RefreshAvailableReportPath()
        {
            var availablePath = GetAvailableReportPath();
            _currentReportRootPath = availablePath;
            return availablePath != null;
        }

        /// <summary>
        /// 获取当前使用的报告根路径
        /// </summary>
        /// <returns></returns>
        public string GetCurrentReportRootPath()
        {
            return _currentReportRootPath;
        }

        /// <summary>
        /// 从配置文件加载报告类型映射
        /// </summary>
        /// <returns></returns>
        private Dictionary<string, string> LoadReportTypeMapping()
        {
            var mapping = new Dictionary<string, string>();
            var reportTypesConfig = ConfigurationManager.AppSettings["ReportTypes"];
            
            if (!string.IsNullOrEmpty(reportTypesConfig))
            {
                var types = reportTypesConfig.Split(';');
                foreach (var type in types)
                {
                    var parts = type.Split(':');
                    if (parts.Length == 2)
                    {
                        mapping[parts[0]] = parts[1];
                    }
                }
            }
            
            return mapping;
        }

        /// <summary>
        /// 获取报告类型列表
        /// </summary>
        /// <returns></returns>
        public List<string> GetReportTypes()
        {
            return _reportTypeMapping.Keys.ToList();
        }

        /// <summary>
        /// 从配置文件加载GC设备代码列表
        /// </summary>
        /// <returns></returns>
        private List<string> LoadGCDeviceCodes()
        {
            var deviceCodes = new List<string>();

            try
            {
                var gcDeviceCodesConfig = ConfigurationManager.AppSettings["GCDeviceCodes"];

                if (!string.IsNullOrEmpty(gcDeviceCodesConfig))
                {
                    var codes = gcDeviceCodesConfig.Split(';');
                    foreach (var code in codes)
                    {
                        var trimmedCode = code?.Trim();
                        if (!string.IsNullOrEmpty(trimmedCode))
                        {
                            deviceCodes.Add(trimmedCode);
                        }
                    }
                }
                else
                {
                    // 如果配置为空，记录调试信息但不抛出异常
                    System.Diagnostics.Debug.WriteLine("警告：未找到GCDeviceCodes配置项或配置为空");
                }
            }
            catch (Exception ex)
            {
                // 记录错误但不抛出异常，返回空列表
                System.Diagnostics.Debug.WriteLine($"加载GC设备代码配置时发生错误: {ex.Message}");
            }

            return deviceCodes;
        }

        /// <summary>
        /// 获取GC设备代码列表
        /// </summary>
        /// <returns></returns>
        public List<string> GetGCDeviceCodes()
        {
            return _gcDeviceCodes.ToList();
        }

        /// <summary>
        /// 构建报告路径
        /// </summary>
        /// <param name="stationName"></param>
        /// <param name="flowComputerTag"></param>
        /// <param name="reportType"></param>
        /// <returns></returns>
        public string BuildReportPath(string stationName, string flowComputerTag, string reportType)
        {
            if (string.IsNullOrEmpty(stationName) || string.IsNullOrEmpty(flowComputerTag))
            {
                throw new ArgumentException("站点名称和流量计算机标签不能为空");
            }

            // 检查并更新当前可用的路径
            if (!RefreshAvailableReportPath())
            {
                throw new InvalidOperationException("报告路径不存在或无法访问，请检查配置中的主路径和备用路径");
            }

            // 如果是色谱分析仪，使用特殊的路径结构
            if (stationName == "色谱分析仪")
            {
                return BuildGCReportPath(flowComputerTag);
            }

            // 常规报告路径构建
            if (string.IsNullOrEmpty(reportType))
            {
                throw new ArgumentException("报告类型不能为空");
            }

            if (!_reportTypeMapping.ContainsKey(reportType))
            {
                throw new ArgumentException($"未找到报告类型 '{reportType}' 的映射配置");
            }

            // 将流量计算机标签中的下划线替换为短横线
            var formattedFlowComputerTag = flowComputerTag.Replace("_", "-");
            var reportTypeFolder = _reportTypeMapping[reportType];

            return Path.Combine(_currentReportRootPath, stationName, formattedFlowComputerTag, reportTypeFolder);
        }

        /// <summary>
        /// 构建GC报告路径
        /// </summary>
        /// <param name="deviceCode">GC设备代码</param>
        /// <returns></returns>
        public string BuildGCReportPath(string deviceCode)
        {
            if (string.IsNullOrWhiteSpace(deviceCode))
            {
                throw new ArgumentException("GC设备代码不能为空或仅包含空白字符", nameof(deviceCode));
            }

            // 检查设备代码是否在配置的列表中
            if (_gcDeviceCodes != null && _gcDeviceCodes.Count > 0 && !_gcDeviceCodes.Contains(deviceCode))
            {
                throw new ArgumentException($"设备代码 '{deviceCode}' 不在配置的GC设备代码列表中", nameof(deviceCode));
            }

            // 检查并更新当前可用的路径
            if (!RefreshAvailableReportPath())
            {
                throw new InvalidOperationException("报告路径不存在或无法访问，请检查配置中的主路径和备用路径");
            }

            // 验证设备代码不包含非法字符
            var invalidChars = Path.GetInvalidFileNameChars();
            if (deviceCode.IndexOfAny(invalidChars) >= 0)
            {
                throw new ArgumentException($"设备代码 '{deviceCode}' 包含非法字符", nameof(deviceCode));
            }

            // GC报告路径结构：[ReportRootDirectory]\GC\[DeviceCode]\
            return Path.Combine(_currentReportRootPath, "GC", deviceCode);
        }

        /// <summary>
        /// 计算归档时间点：当前日期减去指定月数
        /// </summary>
        /// <returns></returns>
        public DateTime GetArchiveThresholdDate()
        {
            var currentDate = DateTime.Now;
            return new DateTime(currentDate.Year, currentDate.Month, 1).AddMonths(-_archiveThresholdMonths);
        }

        /// <summary>
        /// 判断指定年月是否已被归档
        /// </summary>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <returns></returns>
        public bool IsMonthArchived(int year, int month)
        {
            var monthDate = new DateTime(year, month, 1);
            var archiveThreshold = GetArchiveThresholdDate();
            return monthDate < archiveThreshold;
        }

        /// <summary>
        /// 获取日期范围内的所有月份
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public List<(int Year, int Month)> GetMonthsInRange(DateTime startDate, DateTime endDate)
        {
            var months = new List<(int Year, int Month)>();
            var current = new DateTime(startDate.Year, startDate.Month, 1);
            var end = new DateTime(endDate.Year, endDate.Month, 1);

            while (current <= end)
            {
                months.Add((current.Year, current.Month));
                current = current.AddMonths(1);
            }

            return months;
        }

        /// <summary>
        /// 构建归档子目录路径
        /// </summary>
        /// <param name="baseDirectoryPath"></param>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <returns></returns>
        public string BuildArchiveSubPath(string baseDirectoryPath, int year, int month)
        {
            var archiveFolder = $"{year:D4}{month:D2}";
            return Path.Combine(baseDirectoryPath, archiveFolder);
        }

        /// <summary>
        /// 获取指定目录下符合条件的文件（支持归档查询）
        /// </summary>
        /// <param name="directoryPath"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public List<ReportFile> GetReportFiles(string directoryPath, DateTime startDate, DateTime endDate)
        {
            return GetReportFilesAsync(directoryPath, startDate, endDate).Result;
        }

        /// <summary>
        /// 获取查询的详细调试信息
        /// </summary>
        /// <param name="directoryPath"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public string GetQueryDebugInfo(string directoryPath, DateTime startDate, DateTime endDate)
        {
            var debugInfo = new System.Text.StringBuilder();
            debugInfo.AppendLine($"=== 查询调试信息 ===");
            debugInfo.AppendLine($"基础路径: {directoryPath}");
            debugInfo.AppendLine($"查询日期范围: {startDate:yyyy-MM-dd} 至 {endDate:yyyy-MM-dd}");
            debugInfo.AppendLine($"当前日期: {DateTime.Now:yyyy-MM-dd}");
            debugInfo.AppendLine($"归档阈值: {_archiveThresholdMonths} 个月");
            debugInfo.AppendLine($"归档分界线: {GetArchiveThresholdDate():yyyy-MM-dd}");
            debugInfo.AppendLine();

            var monthsInRange = GetMonthsInRange(startDate, endDate);
            debugInfo.AppendLine($"查询范围内的月份 ({monthsInRange.Count} 个):");
            
            // 按搜索路径分组显示
            var pathGroups = new Dictionary<string, List<(int Year, int Month)>>();
            
            foreach (var month in monthsInRange)
            {
                var searchPath = directoryPath;
                if (IsMonthArchived(month.Year, month.Month))
                {
                    searchPath = BuildArchiveSubPath(directoryPath, month.Year, month.Month);
                }
                
                if (!pathGroups.ContainsKey(searchPath))
                {
                    pathGroups[searchPath] = new List<(int Year, int Month)>();
                }
                pathGroups[searchPath].Add(month);
            }
            
            debugInfo.AppendLine($"实际搜索路径 ({pathGroups.Count} 个):");
            
            foreach (var pathGroup in pathGroups)
            {
                var searchPath = pathGroup.Key;
                var months = pathGroup.Value;
                var isArchivePath = months.Any(m => IsMonthArchived(m.Year, m.Month));
                var monthList = string.Join(", ", months.Select(m => $"{m.Year}-{m.Month:D2}"));
                
                debugInfo.AppendLine($"  路径: {searchPath}");
                debugInfo.AppendLine($"    类型: {(isArchivePath ? "归档目录" : "当前目录")}");
                debugInfo.AppendLine($"    对应月份: {monthList}");
                debugInfo.AppendLine($"    目录存在: {Directory.Exists(searchPath)}");
                
                if (Directory.Exists(searchPath))
                {
                    try
                    {
                        var directory = new DirectoryInfo(searchPath);
                        var allFiles = directory.GetFiles("*.*")
                            .Where(f => f.Extension.ToLower() == ".pdf" || f.Extension.ToLower() == ".xls" || f.Extension.ToLower() == ".xlsx")
                            .ToList();
                        
                        debugInfo.AppendLine($"    报告文件总数: {allFiles.Count}");
                        
                        var filesWithFileNameDate = 0;
                        var filesWithCreationTime = 0;
                        var qualifiedFiles = 0;
                        
                        foreach (var file in allFiles)
                        {
                            var fileNameDate = ParseDateFromFileName(file.Name);
                            if (fileNameDate.HasValue)
                            {
                                filesWithFileNameDate++;
                                if (fileNameDate.Value.Date >= startDate.Date && fileNameDate.Value.Date <= endDate.Date)
                                {
                                    qualifiedFiles++;
                                }
                            }
                            else
                            {
                                filesWithCreationTime++;
                                if (file.CreationTime.Date >= startDate.Date && file.CreationTime.Date <= endDate.Date)
                                {
                                    qualifiedFiles++;
                                }
                            }
                        }
                        
                        debugInfo.AppendLine($"    可解析文件名日期的文件: {filesWithFileNameDate}");
                        debugInfo.AppendLine($"    使用创建时间的文件: {filesWithCreationTime}");
                        debugInfo.AppendLine($"    符合日期范围的文件: {qualifiedFiles}");
                    }
                    catch (Exception ex)
                    {
                        debugInfo.AppendLine($"    读取目录出错: {ex.Message}");
                    }
                }
                debugInfo.AppendLine();
            }
            
            return debugInfo.ToString();
        }

        /// <summary>
        /// 获取指定目录下符合条件的文件（异步版本，支持归档查询）
        /// </summary>
        /// <param name="directoryPath"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<ReportFile>> GetReportFilesAsync(string directoryPath, DateTime startDate, DateTime endDate)
        {
            var reportFiles = new List<ReportFile>();
            var errors = new List<string>();

            try
            {
                if (!Directory.Exists(directoryPath))
                {
                    throw new DirectoryNotFoundException($"基础目录不存在: {directoryPath}");
                }

                // 获取查询范围内的所有月份
                var monthsInRange = GetMonthsInRange(startDate, endDate);
                
                // 按搜索路径分组，避免重复搜索同一个目录
                var pathGroups = new Dictionary<string, List<(int Year, int Month)>>();
                
                foreach (var month in monthsInRange)
                {
                    var searchPath = directoryPath;
                    if (IsMonthArchived(month.Year, month.Month))
                    {
                        searchPath = BuildArchiveSubPath(directoryPath, month.Year, month.Month);
                    }
                    
                    if (!pathGroups.ContainsKey(searchPath))
                    {
                        pathGroups[searchPath] = new List<(int Year, int Month)>();
                    }
                    pathGroups[searchPath].Add(month);
                }

                // 为每个唯一的搜索路径创建查询任务
                var tasks = pathGroups.Select(async pathGroup =>
                {
                    var searchPath = pathGroup.Key;
                    var monthsForPath = pathGroup.Value;
                    var pathFiles = new List<ReportFile>();

                    try
                    {
                        if (Directory.Exists(searchPath))
                        {
                            var isArchiveDir = monthsForPath.Any(m => IsMonthArchived(m.Year, m.Month));
                            var pathFilesResult = await GetFilesFromDirectoryAsync(searchPath, startDate, endDate, isArchiveDir);
                            pathFiles.AddRange(pathFilesResult);
                        }
                        else
                        {
                            // 检查是否有归档月份的目录不存在
                            var missingArchiveMonths = monthsForPath.Where(m => IsMonthArchived(m.Year, m.Month)).ToList();
                            if (missingArchiveMonths.Any())
                            {
                                var monthList = string.Join(", ", missingArchiveMonths.Select(m => $"{m.Year}-{m.Month:D2}"));
                                errors.Add($"归档目录不存在: {searchPath} (月份: {monthList})");
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        var monthList = string.Join(", ", monthsForPath.Select(m => $"{m.Year}-{m.Month:D2}"));
                        errors.Add($"搜索路径 {searchPath} 时发生错误: {ex.Message} (月份: {monthList})");
                    }

                    return pathFiles;
                });

                // 等待所有任务完成
                var results = await Task.WhenAll(tasks);
                
                // 合并所有结果
                foreach (var result in results)
                {
                    reportFiles.AddRange(result);
                }

                // 记录去重前的文件数量（用于调试）
                var totalFilesBeforeDedup = reportFiles.Count;
                
                // 按文件完整路径去重（防止同一文件被多次添加）
                reportFiles = reportFiles
                    .GroupBy(f => f.FullPath)
                    .Select(g => g.First())
                    .OrderByDescending(f => f.CreationTime)
                    .ToList();

                // 记录去重信息
                var duplicatesRemoved = totalFilesBeforeDedup - reportFiles.Count;
                if (duplicatesRemoved > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"去重处理：共移除 {duplicatesRemoved} 个重复文件，最终结果 {reportFiles.Count} 个文件");
                }

                // 如果有错误但找到了文件，记录错误信息到文件属性中（用于调试）
                if (errors.Count > 0)
                {
                    var errorSummary = string.Join("\n", errors);
                    System.Diagnostics.Debug.WriteLine($"查询过程中的警告信息:\n{errorSummary}");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"获取文件列表时发生错误: {ex.Message}", ex);
            }

            return reportFiles;
        }

        /// <summary>
        /// 从文件名中解析日期时间
        /// </summary>
        /// <param name="fileName"></param>
        /// <returns></returns>
        private DateTime? ParseDateFromFileName(string fileName)
        {
            try
            {
                // 常见的日期时间格式模式
                var dateTimePatterns = new[]
                {
                    // 特定格式：HOURLY_REPORT_07-04-2025_15-00-00 (dd-MM-yyyy_HH-mm-ss)
                    @"(\d{1,2})-(\d{1,2})-(\d{4})_(\d{1,2})-(\d{1,2})-(\d{1,2})", // dd-MM-yyyy_HH-mm-ss
                    
                    // 日期时间组合格式
                    @"(\d{1,2})-(\d{1,2})-(\d{4})\s+(\d{1,2}):(\d{1,2})",         // dd-MM-yyyy HH:mm
                    @"(\d{1,2})_(\d{1,2})_(\d{4})\s+(\d{1,2}):(\d{1,2})",         // dd_MM_yyyy HH:mm
                    @"(\d{4})-(\d{1,2})-(\d{1,2})\s+(\d{1,2}):(\d{1,2})",         // yyyy-MM-dd HH:mm
                    @"(\d{4})_(\d{1,2})_(\d{1,2})\s+(\d{1,2}):(\d{1,2})",         // yyyy_MM_dd HH:mm
                    @"(\d{1,2})-(\d{1,2})-(\d{4})_(\d{1,2}):(\d{1,2})",           // dd-MM-yyyy_HH:mm
                    @"(\d{1,2})_(\d{1,2})_(\d{4})_(\d{1,2}):(\d{1,2})",           // dd_MM_yyyy_HH:mm
                    @"(\d{4})-(\d{1,2})-(\d{1,2})_(\d{1,2}):(\d{1,2})",           // yyyy-MM-dd_HH:mm
                    @"(\d{4})_(\d{1,2})_(\d{1,2})_(\d{1,2}):(\d{1,2})",           // yyyy_MM_dd_HH:mm
                    @"(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})",                       // yyyyMMddHHmm
                    @"(\d{2})(\d{2})(\d{4})(\d{2})(\d{2})",                       // ddMMyyyyHHmm
                };

                // 首先尝试解析包含时间的格式
                foreach (var pattern in dateTimePatterns)
                {
                    var match = System.Text.RegularExpressions.Regex.Match(fileName, pattern);
                    if (match.Success)
                    {
                        var groups = match.Groups;
                        
                        try
                        {
                            if (groups.Count == 7) // 6个捕获组 + 完整匹配 (dd-MM-yyyy_HH-mm-ss)
                            {
                                // 解析HOURLY_REPORT_07-04-2025_15-00-00格式
                                var day = int.Parse(groups[1].Value);
                                var month = int.Parse(groups[2].Value);
                                var year = int.Parse(groups[3].Value);
                                var hour = int.Parse(groups[4].Value);
                                var minute = int.Parse(groups[5].Value);
                                var second = int.Parse(groups[6].Value);
                                
                                if (IsValidDateTime(year, month, day, hour, minute, second))
                                    return new DateTime(year, month, day, hour, minute, second);
                            }
                            else if (groups.Count == 6) // 5个捕获组 + 完整匹配
                            {
                                // 解析为年月日时分
                                if (pattern.Contains("yyyy") && pattern.IndexOf("yyyy") <= 4) // yyyy在前
                                {
                                    var year = int.Parse(groups[1].Value);
                                    var month = int.Parse(groups[2].Value);
                                    var day = int.Parse(groups[3].Value);
                                    var hour = int.Parse(groups[4].Value);
                                    var minute = int.Parse(groups[5].Value);
                                    
                                    if (IsValidDateTime(year, month, day, hour, minute))
                                        return new DateTime(year, month, day, hour, minute, 0);
                                }
                                else // dd在前
                                {
                                    var day = int.Parse(groups[1].Value);
                                    var month = int.Parse(groups[2].Value);
                                    var year = int.Parse(groups[3].Value);
                                    var hour = int.Parse(groups[4].Value);
                                    var minute = int.Parse(groups[5].Value);
                                    
                                    if (IsValidDateTime(year, month, day, hour, minute))
                                        return new DateTime(year, month, day, hour, minute, 0);
                                }
                            }
                        }
                        catch
                        {
                            // 忽略解析错误，继续尝试下一个模式
                        }
                    }
                }

                // 如果没有找到时间信息，尝试仅日期格式
                var dateOnlyPatterns = new[]
                {
                    @"(\d{1,2})-(\d{1,2})-(\d{4})",     // dd-MM-yyyy
                    @"(\d{1,2})_(\d{1,2})_(\d{4})",     // dd_MM_yyyy
                    @"(\d{4})-(\d{1,2})-(\d{1,2})",     // yyyy-MM-dd
                    @"(\d{4})_(\d{1,2})_(\d{1,2})",     // yyyy_MM_dd
                    @"(\d{4})(\d{2})(\d{2})",           // yyyyMMdd
                    @"(\d{2})(\d{2})(\d{4})",           // ddMMyyyy
                };

                foreach (var pattern in dateOnlyPatterns)
                {
                    var match = System.Text.RegularExpressions.Regex.Match(fileName, pattern);
                    if (match.Success)
                    {
                        var groups = match.Groups;
                        
                        // 根据不同的模式解析日期，默认时间为12:00
                        if (pattern.Contains("yyyy") && pattern.IndexOf("yyyy") <= 4) // yyyy开头
                        {
                            if (int.TryParse(groups[1].Value, out var year) &&
                                int.TryParse(groups[2].Value, out var month) &&
                                int.TryParse(groups[3].Value, out var day))
                            {
                                if (IsValidDate(year, month, day))
                                    return new DateTime(year, month, day, 12, 0, 0); // 默认12:00
                            }
                        }
                        else if (pattern.Contains("dd") && pattern.IndexOf("dd") <= 4) // dd开头
                        {
                            if (int.TryParse(groups[1].Value, out var day) &&
                                int.TryParse(groups[2].Value, out var month) &&
                                int.TryParse(groups[3].Value, out var year))
                            {
                                if (IsValidDate(year, month, day))
                                    return new DateTime(year, month, day, 12, 0, 0); // 默认12:00
                            }
                        }
                        else if (groups.Count == 4) // 通用3组匹配
                        {
                            // 尝试不同的顺序
                            var values = new[] { groups[1].Value, groups[2].Value, groups[3].Value }
                                .Select(v => int.TryParse(v, out var i) ? i : 0).ToArray();
                            
                            // 尝试 yyyy-MM-dd 格式
                            if (values[0] > 1900 && values[0] < 2100 && values[1] >= 1 && values[1] <= 12 && values[2] >= 1 && values[2] <= 31)
                            {
                                if (IsValidDate(values[0], values[1], values[2]))
                                    return new DateTime(values[0], values[1], values[2], 12, 0, 0); // 默认12:00
                            }
                            
                            // 尝试 dd-MM-yyyy 格式
                            if (values[2] > 1900 && values[2] < 2100 && values[1] >= 1 && values[1] <= 12 && values[0] >= 1 && values[0] <= 31)
                            {
                                if (IsValidDate(values[2], values[1], values[0]))
                                    return new DateTime(values[2], values[1], values[0], 12, 0, 0); // 默认12:00
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"解析文件名日期时间时出错: {fileName}, 错误: {ex.Message}");
            }

            return null;
        }

        /// <summary>
        /// 验证日期时间是否有效
        /// </summary>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="day"></param>
        /// <param name="hour"></param>
        /// <param name="minute"></param>
        /// <returns></returns>
        private bool IsValidDateTime(int year, int month, int day, int hour, int minute)
        {
            return IsValidDateTime(year, month, day, hour, minute, 0);
        }

        /// <summary>
        /// 验证日期时间是否有效（包含秒）
        /// </summary>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="day"></param>
        /// <param name="hour"></param>
        /// <param name="minute"></param>
        /// <param name="second"></param>
        /// <returns></returns>
        private bool IsValidDateTime(int year, int month, int day, int hour, int minute, int second)
        {
            try
            {
                if (year < 1900 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31 ||
                    hour < 0 || hour > 23 || minute < 0 || minute > 59 || second < 0 || second > 59)
                    return false;
                    
                var dateTime = new DateTime(year, month, day, hour, minute, second);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 验证日期是否有效
        /// </summary>
        /// <param name="year"></param>
        /// <param name="month"></param>
        /// <param name="day"></param>
        /// <returns></returns>
        private bool IsValidDate(int year, int month, int day)
        {
            try
            {
                if (year < 1900 || year > 2100 || month < 1 || month > 12 || day < 1 || day > 31)
                    return false;
                    
                var date = new DateTime(year, month, day);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 从指定目录获取文件（异步）
        /// </summary>
        /// <param name="directoryPath"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="isArchiveDirectory">是否为归档目录</param>
        /// <returns></returns>
        private async Task<List<ReportFile>> GetFilesFromDirectoryAsync(string directoryPath, DateTime startDate, DateTime endDate, bool isArchiveDirectory = false)
        {
            return await Task.Run(() =>
            {
                var files = new List<ReportFile>();
                
                try
                {
                    var directory = new DirectoryInfo(directoryPath);
                    var fileInfos = directory.GetFiles("*.*")
                        .Where(f => f.Extension.ToLower() == ".pdf" || f.Extension.ToLower() == ".xls" || f.Extension.ToLower() == ".xlsx");

                    foreach (var file in fileInfos)
                    {
                        var shouldInclude = false;
                        
                        // 首先尝试从文件名解析日期
                        var fileNameDate = ParseDateFromFileName(file.Name);
                        if (fileNameDate.HasValue)
                        {
                            // 使用文件名中的日期进行筛选
                            shouldInclude = fileNameDate.Value.Date >= startDate.Date && fileNameDate.Value.Date <= endDate.Date;
                        }
                        else
                        {
                            // 如果无法从文件名解析日期，fallback到创建时间
                            shouldInclude = file.CreationTime.Date >= startDate.Date && file.CreationTime.Date <= endDate.Date;
                        }
                        
                        if (shouldInclude)
                        {
                            files.Add(new ReportFile
                            {
                                FileName = file.Name,
                                CreationTime = file.CreationTime,
                                FullPath = file.FullName,
                                FileSize = file.Length,
                                // 添加解析出的文件名日期信息
                                FileNameDate = fileNameDate
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception($"读取目录 {directoryPath} 时发生错误: {ex.Message}", ex);
                }

                return files;
            });
        }

        /// <summary>
        /// 获取当前路径状态信息
        /// </summary>
        /// <returns></returns>
        public string GetPathStatusInfo()
        {
            var primaryStatus = IsPathAccessible(_reportRootPath) ? "可用" : "不可用";
            var backupStatus = IsPathAccessible(_reportRootPathBackup) ? "可用" : "不可用";
            var currentPath = _currentReportRootPath ?? "无";

            return $"主路径({_reportRootPath}): {primaryStatus}\n" +
                   $"备用路径({_reportRootPathBackup}): {backupStatus}\n" +
                   $"当前使用路径: {currentPath}";
        }
    }

    /// <summary>
    /// 报告文件信息类
    /// </summary>
    public class ReportFile
    {
        public string FileName { get; set; }
        public DateTime CreationTime { get; set; }
        public string FullPath { get; set; }
        public long FileSize { get; set; }
        public DateTime? FileNameDate { get; set; }
        
        public string FormattedCreationTime => CreationTime.ToString("yyyy-MM-dd HH:mm:ss");
        public string FormattedFileSize => FormatFileSize(FileSize);
        public string FormattedFileNameDate => FileNameDate?.ToString("yyyy-MM-dd HH:mm") ?? "无法解析";

        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }
    }
} 