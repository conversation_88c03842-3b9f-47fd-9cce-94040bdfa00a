﻿using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.UserSkins;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace ReportManager
{
    internal static class Program
    {
        // Win32 API 声明
        [DllImport("user32.dll")]
        private static extern bool ShowWindow(IntPtr hWnd, int cmdShow);

        [DllImport("user32.dll")]
        private static extern bool SetForegroundWindow(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool BringWindowToTop(IntPtr hWnd);

        [DllImport("user32.dll")]
        private static extern bool IsIconic(IntPtr hWnd);

        // ShowWindow 命令常量
        private const int SW_RESTORE = 9;

        // 定义互斥体名称
        private static string mutexName = "DPLNGReportManager_ESDF0DFAVZZDFS3-9C85-4C1F-6666-6C86E45FBHJ7E23";
        
        private static Mutex mutex;

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // 尝试创建命名互斥体
            mutex = new Mutex(true, mutexName, out bool createdNew);

            if (createdNew)
            {
                try
                {
                    // 这是第一个实例，正常运行应用程序
                    Application.Run(new Form1());
                }
                finally
                {
                    // 确保在程序退出时释放互斥体
                    mutex?.ReleaseMutex();
                    mutex?.Close();
                }
            }
            else
            {
                // 已经有一个实例在运行，尝试激活现有窗口
                ActivateOtherWindow();
                
                // 释放当前的互斥体引用并退出
                mutex?.Close();
                return;
            }
        }

        private static void ActivateOtherWindow()
        {
            try
            {
                // 简化的激活逻辑：只处理任务栏最小化的情况
                string currentProcessName = System.Diagnostics.Process.GetCurrentProcess().ProcessName;
                
                foreach (System.Diagnostics.Process proc in System.Diagnostics.Process.GetProcessesByName(currentProcessName))
                {
                    // 跳过当前进程
                    if (proc.Id == System.Diagnostics.Process.GetCurrentProcess().Id)
                        continue;
                        
                    IntPtr handle = proc.MainWindowHandle;
                    if (handle != IntPtr.Zero)
                    {
                        // 如果窗口被最小化，则还原它
                        if (IsIconic(handle))
                        {
                            ShowWindow(handle, SW_RESTORE);
                        }
                        
                        // 将窗口带到前台
                        BringWindowToTop(handle);
                        SetForegroundWindow(handle);
                        break;
                    }
                }
            }
            catch (Exception)
            {
                // 忽略激活窗口时的错误
            }
        }


    }
}