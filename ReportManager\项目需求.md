现在需要在已有的winform项目中实现如下功能：
1.项目基于DevExpress winform，.net framwork 4.8，已经有一个form和三个combox控件，两个date控件，一个按钮控件，一个gridcontrol控件；
2.第一个combobox控件用来在窗口启动时，显示从数据库的selection_table里获取所有的station名，注意去掉重复的站名；
第二个combox控件用来在用户在第一个combobox里选择好station后，显示这个station包含的所有flowcomputertag列表，注意同样去掉重复值；
第三个combox控件用来选择报告类型，为固定列表，包括：小时报告，日报告，周报告等
两个date控件用来让用户选择一个日期间隔；
按钮用来根据选择的station和flowcomputertag，找到指定的报告目录，再根据日期间隔，筛选出创建时间是这个时间间隔内的所有PDF和excel文件，显示到gridcontrol控件里，一列是文件名，一列是创建时间；
3.报告路径组成如下：
报告根目录+站名+流量计算机名+报告类型
例如：报告根目录为"D:\Report\"，站名为"东部电厂"，流量计算机名为"FC_11670A"，报告类型为"小时报告"，则实际报告路径为：
D:\Report\东部电厂\FC-11670A\HourlyReport\
程序需要在用户点击按钮后列出这个路径下文件创建时间符合要求的所有PDF和excel文件到gridcontrol控件里
4.报告根目录、数据库连接参数(包括表名selection_table)和报告类型列表，可以放到项目的配置文件里，以便现场修改。


请仔细阅读并理解整个项目，然后新增如下功能：
1. 在cbStation列表里增加一个固定的选项“色谱分析仪”，并且始终在列表第一个位置；同时请在程序配置文件里增加一个色谱分析仪设备列表，可以预先输入进去，比如AT_2121/AT2122等设备代码；
2. 当选择“色谱分析仪”后，cbReportType就被锁定，不能再选择，cbFlowComputer列表里显示从配置文件获取的设备代码列表；此时用户可以选择要查看报告的设备代码，然后通过两个日期选择控件选择要查看的报告的开始和结束日期，以文件名里包含的日期为准(可以参考现有的根据文件名来筛选日期的代码)；
3.色谱分析仪报告的目录为报告根目录下“GC”文件夹里的以设备代码为名的文件夹，例如：报告根目录为"D:\Report\"，设备代码为"AT_2121"，则实际报告路径为：D:\Report\AT_2121\; 注意报告根目录等参数已经在app.config中配置.
4.色谱分析仪报告的格式与其他报告格式不同，为excel文件，与程序中现有报告类似，用户可以在gridcontrol1中双击报告名，用系统已安装好的excel打开此报告。
